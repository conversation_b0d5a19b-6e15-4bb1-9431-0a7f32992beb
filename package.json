{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "build:production": "NODE_ENV=production next build", "start:production": "NODE_ENV=production next start", "docker:build": "docker build -t cbnews-admin .", "docker:run": "docker run -p 3000:3000 --env-file .env.local cbnews-admin", "docker:dev": "docker-compose up --build"}, "dependencies": {"@hookform/resolvers": "5.1.1", "@next/swc-wasm-nodejs": "15.4.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.9", "@types/node": "24.0.0", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "4.1.0", "embla-carousel-react": "^8.6.0", "eslint": "9.28.0", "eslint-config-next": "15.3.3", "input-otp": "^1.4.2", "lucide-react": "^0.514.0", "next": "15.3.3", "next-themes": "^0.4.6", "postcss": "8.5.5", "react": "19.1.0", "react-day-picker": "9.7.0", "react-dom": "19.1.0", "react-hook-form": "^7.57.0", "react-resizable-panels": "3.0.2", "recharts": "^2.15.3", "sonner": "2.0.5", "tailwind-merge": "3.3.1", "tailwindcss": "4.1.9", "tailwindcss-animate": "^1.0.7", "typescript": "5.8.3", "vaul": "1.1.2", "zod": "^3.25.61"}}