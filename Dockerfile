# Etapa 1: Instalar dependências e construir o projeto
FROM node:20-alpine AS builder

# Instalar pnpm
RUN npm install -g pnpm

WORKDIR /app

# Copiar arquivos de dependência e instalar
COPY package.json pnpm-lock.yaml ./
RUN pnpm install

# Copiar o restante do código-fonte
COPY . .

# Construir a aplicação
RUN pnpm build

# Etapa 2: Criar a imagem de produção
FROM node:20-alpine AS runner

WORKDIR /app

# Copiar o usuário e grupo 'nextjs' da imagem do builder
COPY --from=builder /etc/passwd /etc/passwd
COPY --from=builder /etc/group /etc/group

# Copiar os artefatos de compilação da etapa anterior
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/public ./public

# Mudar para o usuário não-root 'nextjs'
USER nextjs

EXPOSE 3000

ENV PORT 3000

# Iniciar a aplicação
CMD ["pnpm", "start"]