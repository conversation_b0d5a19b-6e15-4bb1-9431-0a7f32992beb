# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Next.js build output
.next/
out/

# Production build
/build

# Environment files
.env
.env*.local
.env.development
.env.test

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Documentation
README.md
docs/
*.md

# Testing
coverage/
.nyc_output

# Misc
.vercel
.bolt/

# Docker
Dockerfile*
docker-compose*
.dockerignore
