/\*_ @type {import('next').NextConfig} _/
const nextConfig = {
output: 'export',
eslint: {
ignoreDuringBuilds: true,
},
images: { unoptimized: true },
};

module.exports = nextConfig;
#14 [builder 6/6] RUN pnpm build
#14 0.886 
#14 0.886 > nextjs@0.1.0 build /app
#14 0.886 > next build
#14 0.886 
#14 2.013    ▲ Next.js 15.3.3
#14 2.014    - Environments: .env.production
#14 2.014    - Experiments (use with caution):
#14 2.014      ✓ optimizeCss
#14 2.014 
#14 2.030    Creating an optimized production build ...
#14 32.48  ✓ Compiled successfully in 25.0s
#14 32.49    Skipping linting
#14 32.49    Checking validity of types ...
#14 44.35    Collecting page data ...
#14 47.04    Generating static pages (0/4) ...
#14 47.74 Error occurred prerendering page "/404". Read more: https://nextjs.org/docs/messages/prerender-error
#14 47.74 Error: Cannot find module 'critters'
#14 47.74 Require stack:
#14 47.74 - /app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/next-server/pages.runtime.prod.js
#14 47.74 - /app/.next/server/pages/_document.js
#14 47.74 - /app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/require.js
#14 47.74 - /app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/load-components.js
#14 47.74 - /app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/utils.js
#14 47.74 - /app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/worker.js
#14 47.74 - /app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/jest-worker/processChild.js
#14 47.74     at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)
#14 47.74     at /app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/require-hook.js:55:36
#14 47.74     at Module._load (node:internal/modules/cjs/loader:1043:27)
#14 47.74     at Module.require (node:internal/modules/cjs/loader:1298:19)
#14 47.74     at mod.require (/app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/require-hook.js:65:28)
#14 47.74     at require (node:internal/modules/helpers:182:18)
#14 47.74     at Object.critters (/app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/next-server/pages.runtime.prod.js:14:17567)
#14 47.74     at r (/app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/next-server/pages.runtime.prod.js:14:18154)
#14 47.74     at /app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/next-server/pages.runtime.prod.js:14:14275
#14 47.74     at o (/app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/next-server/pages.runtime.prod.js:14:14570)
#14 47.74 Export encountered an error on /_error: /404, exiting the build.
#14 48.10  ⨯ Next.js build worker exited with code: 1 and signal: null
#14 48.17  ELIFECYCLE  Command failed with exit code 1.
#14 ERROR: process "/bin/sh -c pnpm build" did not complete successfully: exit code: 1
------
 > [builder 6/6] RUN pnpm build:
47.74     at Module.require (node:internal/modules/cjs/loader:1298:19)
47.74     at mod.require (/app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/require-hook.js:65:28)
47.74     at require (node:internal/modules/helpers:182:18)
47.74     at Object.critters (/app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/next-server/pages.runtime.prod.js:14:17567)
47.74     at r (/app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/next-server/pages.runtime.prod.js:14:18154)
47.74     at /app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/next-server/pages.runtime.prod.js:14:14275
47.74     at o (/app/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/next-server/pages.runtime.prod.js:14:14570)
47.74 Export encountered an error on /_error: /404, exiting the build.
48.10  ⨯ Next.js build worker exited with code: 1 and signal: null
48.17  ELIFECYCLE  Command failed with exit code 1.
------
Dockerfile:33
--------------------
  31 |     
  32 |     # Build the application
  33 | >>> RUN pnpm build
  34 |     
  35 |     # Stage 3: Production runtime
--------------------
ERROR: failed to solve: process "/bin/sh -c pnpm build" did not complete successfully: exit code: 1
##########################################
### Error
### Thu, 12 Jun 2025 01:10:08 GMT
##########################################